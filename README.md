# DICOM Viewer

A web-based DICOM viewer implementation using Cornerstone.js that allows users to upload and view DICOM (.dcm) files directly in the browser.

## Features

- Upload DICOM files through browser
- View DICOM images with basic tools:
  - Window/Level adjustment (Left mouse button)
  - Pan (Middle mouse button)
  - Zoom (Right mouse button)
- Client-side processing for fast loading
- PHP backend support for file management (optional)

## Installation

1. Place all files in your web server directory
2. Ensure PHP is installed and configured on your server
3. Create an 'uploads' directory and make sure it's writable:
   ```bash
   mkdir uploads
   chmod 777 uploads
   ```
4. Access the viewer through your web browser (e.g., http://localhost/index.html)

## Usage

1. Open the webpage in a modern web browser
2. Click the file input button to select a DICOM file
3. The image will be loaded and displayed in the viewer
4. Use mouse controls to interact with the image:
   - Left click and drag: Adjust window/level
   - Middle click and drag: Pan the image
   - Right click and drag: Zoom in/out

## Dependencies

- Cornerstone.js Core
- Cornerstone Tools
- Cornerstone WADO Image Loader
- DICOM Parser

## Note

This viewer uses client-side processing for DICOM files, which means the files are processed directly in the browser. The PHP backend is included for optional server-side file management.