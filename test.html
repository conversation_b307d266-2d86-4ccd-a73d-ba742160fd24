<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICOM Test</title>
    
    <!-- Cornerstone Dependencies -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/cornerstone-math@0.1.10/dist/cornerstoneMath.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@6.0.10/dist/cornerstoneTools.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.13.2/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/hammerjs@2.0.8/hammer.min.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #viewport {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            background: #000;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>DICOM Test Page</h1>
    
    <div id="status">Ready to load DICOM files...</div>
    
    <div class="controls">
        <input type="file" id="fileInput" accept=".dcm,.dicom">
        <button onclick="testDependencies()">Test Dependencies</button>
        <button onclick="resetViewer()">Reset</button>
    </div>
    
    <div id="viewport"></div>
    
    <script>
        let element;
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Initializing...');
            
            try {
                // Check dependencies
                if (typeof cornerstone === 'undefined') {
                    throw new Error('Cornerstone not loaded');
                }
                if (typeof cornerstoneWADOImageLoader === 'undefined') {
                    throw new Error('WADO Image Loader not loaded');
                }
                if (typeof cornerstoneTools === 'undefined') {
                    throw new Error('Cornerstone Tools not loaded');
                }
                
                // Configure DICOM Image Loader
                cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
                cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
                
                // Configure Cornerstone Tools
                cornerstoneTools.external.cornerstone = cornerstone;
                cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
                cornerstoneTools.external.Hammer = Hammer;
                
                // Initialize Cornerstone Tools
                cornerstoneTools.init();
                
                // Initialize web workers
                cornerstoneWADOImageLoader.webWorkerManager.initialize({
                    maxWebWorkers: 1,
                    startWebWorkersOnDemand: true
                });
                
                // Get viewport element and enable it
                element = document.getElementById('viewport');
                cornerstone.enable(element);
                
                // Add basic tools
                cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
                cornerstoneTools.addTool(cornerstoneTools.PanTool);
                cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
                
                // Set tool states
                cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
                cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 2 });
                cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 4 });
                
                updateStatus('Ready! Select a DICOM file to load.');
                
                // Add file input listener
                document.getElementById('fileInput').addEventListener('change', handleFileSelect);
                
            } catch (error) {
                updateStatus('Error: ' + error.message);
                console.error('Initialization error:', error);
            }
        });
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('Status:', message);
        }
        
        function testDependencies() {
            const deps = {
                'cornerstone': typeof cornerstone !== 'undefined',
                'cornerstoneWADOImageLoader': typeof cornerstoneWADOImageLoader !== 'undefined',
                'cornerstoneTools': typeof cornerstoneTools !== 'undefined',
                'dicomParser': typeof dicomParser !== 'undefined',
                'cornerstoneMath': typeof cornerstoneMath !== 'undefined',
                'Hammer': typeof Hammer !== 'undefined'
            };
            
            console.log('Dependencies:', deps);
            
            const missing = Object.keys(deps).filter(key => !deps[key]);
            if (missing.length > 0) {
                updateStatus('Missing dependencies: ' + missing.join(', '));
            } else {
                updateStatus('All dependencies loaded successfully!');
            }
        }
        
        async function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            try {
                updateStatus('Loading file: ' + file.name);
                
                // Add file to WADO image loader
                const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
                console.log('Created imageId:', imageId);
                
                // Load and display image
                updateStatus('Loading image...');
                const image = await cornerstone.loadImage(imageId);
                console.log('Image loaded:', image);
                
                updateStatus('Displaying image...');
                cornerstone.displayImage(element, image);
                
                updateStatus('Success! DICOM image loaded: ' + file.name);
                
            } catch (error) {
                updateStatus('Error loading DICOM: ' + error.message);
                console.error('DICOM loading error:', error);
            }
        }
        
        function resetViewer() {
            if (element) {
                cornerstone.reset(element);
                updateStatus('Viewer reset. Ready to load new DICOM file.');
            }
        }
    </script>
</body>
</html>
