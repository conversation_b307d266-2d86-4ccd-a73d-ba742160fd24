<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern DICOM Viewer</title>

    <!-- Legacy Cornerstone Dependencies (More Stable) -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/cornerstone-math@0.1.10/dist/cornerstoneMath.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@6.0.10/dist/cornerstoneTools.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.13.2/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/hammerjs@2.0.8/hammer.min.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-x-ray"></i>
                    Modern DICOM Viewer
                </h1>
                <div class="header-controls">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="info-btn" id="infoBtn" title="About">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- File Upload Section -->
                <div class="upload-section">
                    <h3><i class="fas fa-upload"></i> Upload DICOM</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop DICOM files here</p>
                            <p class="upload-text">or</p>
                            <button class="upload-btn" id="uploadBtn">
                                <i class="fas fa-folder-open"></i>
                                Browse Files
                            </button>
                            <input type="file" id="fileInput" accept=".dcm,.dicom" multiple hidden>
                        </div>
                    </div>
                </div>

                <!-- File List -->
                <div class="file-list-section">
                    <h3><i class="fas fa-list"></i> Files</h3>
                    <div class="file-list" id="fileList">
                        <div class="no-files">
                            <i class="fas fa-folder-open"></i>
                            <p>No files loaded</p>
                        </div>
                    </div>
                </div>

                <!-- Image Info -->
                <div class="image-info-section">
                    <h3><i class="fas fa-info"></i> Image Info</h3>
                    <div class="image-info" id="imageInfo">
                        <div class="no-image">
                            <i class="fas fa-image"></i>
                            <p>No image selected</p>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Viewer Area -->
            <div class="viewer-area">
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="tool-group">
                        <button class="tool-btn active" data-tool="WindowLevel" title="Window/Level (W)">
                            <i class="fas fa-adjust"></i>
                        </button>
                        <button class="tool-btn" data-tool="Pan" title="Pan (P)">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                        <button class="tool-btn" data-tool="Zoom" title="Zoom (Z)">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="tool-btn" data-tool="StackScroll" title="Stack Scroll (S)">
                            <i class="fas fa-layer-group"></i>
                        </button>
                    </div>

                    <div class="tool-group">
                        <button class="tool-btn" data-tool="Length" title="Length Measurement (L)">
                            <i class="fas fa-ruler"></i>
                        </button>
                        <button class="tool-btn" data-tool="Angle" title="Angle Measurement (A)">
                            <i class="fas fa-drafting-compass"></i>
                        </button>
                        <button class="tool-btn" data-tool="Rectangle" title="Rectangle ROI (R)">
                            <i class="fas fa-square"></i>
                        </button>
                        <button class="tool-btn" data-tool="Ellipse" title="Ellipse ROI (E)">
                            <i class="fas fa-circle"></i>
                        </button>
                        <button class="tool-btn" data-tool="Probe" title="Probe (Ctrl+P)">
                            <i class="fas fa-crosshairs"></i>
                        </button>
                    </div>

                    <div class="tool-group">
                        <button class="tool-btn" data-action="rotate-left" title="Rotate Left">
                            <i class="fas fa-undo"></i>
                        </button>
                        <button class="tool-btn" data-action="rotate-right" title="Rotate Right">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="tool-btn" data-action="flip-horizontal" title="Flip Horizontal">
                            <i class="fas fa-arrows-alt-h"></i>
                        </button>
                        <button class="tool-btn" data-action="flip-vertical" title="Flip Vertical">
                            <i class="fas fa-arrows-alt-v"></i>
                        </button>
                    </div>

                    <div class="tool-group">
                        <button class="tool-btn" data-action="reset" title="Reset View (Ctrl+R)">
                            <i class="fas fa-home"></i>
                        </button>
                        <button class="tool-btn" data-action="fit" title="Fit to Window (F)">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="tool-btn" data-action="invert" title="Invert Colors (I)">
                            <i class="fas fa-adjust"></i>
                        </button>
                        <button class="tool-btn" data-action="clear-annotations" title="Clear Annotations">
                            <i class="fas fa-eraser"></i>
                        </button>
                        <button class="tool-btn" onclick="testMultiFrame()" title="Test Multi-Frame">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>

                <!-- Viewer Container -->
                <div class="viewer-container">
                    <div class="viewport-wrapper">
                        <div id="viewport" class="viewport"></div>
                        <div class="viewport-overlay">
                            <div class="viewport-info top-left">
                                <div class="patient-info" id="patientInfo"></div>
                            </div>
                            <div class="viewport-info top-right">
                                <div class="image-info-overlay" id="imageInfoOverlay"></div>
                            </div>
                            <div class="viewport-info bottom-left">
                                <div class="window-level-info" id="windowLevelInfo"></div>
                            </div>
                            <div class="viewport-info bottom-right">
                                <div class="zoom-info" id="zoomInfo"></div>
                            </div>
                        </div>

                        <!-- Frame Navigation for Multi-frame DICOM -->
                        <div class="frame-navigation" id="frameNavigation" style="display: none;">
                            <button class="frame-btn" onclick="previousFrame()" title="Previous Frame (←)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <span class="frame-info" id="frameInfo">Frame 1 of 1</span>
                            <button class="frame-btn" onclick="nextFrame()" title="Next Frame (→)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Loading indicator -->
                    <div class="loading-indicator" id="loadingIndicator">
                        <div class="spinner"></div>
                        <p>Loading DICOM image...</p>
                    </div>

                    <!-- Welcome screen -->
                    <div class="welcome-screen" id="welcomeScreen">
                        <div class="welcome-content">
                            <i class="fas fa-x-ray welcome-icon"></i>
                            <h2>Welcome to Modern DICOM Viewer</h2>
                            <p>Upload a DICOM file to get started</p>
                            <button class="welcome-upload-btn" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-upload"></i>
                                Upload DICOM File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal" id="metadataModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-tags"></i> DICOM Metadata</h3>
                <button class="modal-close" onclick="closeModal('metadataModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="metadata-content" id="metadataContent">
                    <div class="loading">Loading metadata...</div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="aboutModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-info-circle"></i> About</h3>
                <button class="modal-close" onclick="closeModal('aboutModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <h4>Modern DICOM Viewer</h4>
                    <p>A professional DICOM viewer built with Cornerstone.js</p>
                    <h5>Features:</h5>
                    <ul>
                        <li>Window/Level adjustment</li>
                        <li>Pan, Zoom, and Stack scrolling</li>
                        <li>Multi-frame DICOM support</li>
                        <li>Measurement tools (Length, Angle)</li>
                        <li>ROI tools (Rectangle, Ellipse)</li>
                        <li>Image transformations (Rotate, Flip, Invert)</li>
                        <li>DICOM metadata viewer</li>
                        <li>Keyboard shortcuts</li>
                        <li>Dark/Light theme</li>
                    </ul>
                    <h5>Keyboard Shortcuts:</h5>
                    <ul>
                        <li><kbd>W</kbd> - Window/Level tool</li>
                        <li><kbd>P</kbd> - Pan tool</li>
                        <li><kbd>Z</kbd> - Zoom tool</li>
                        <li><kbd>L</kbd> - Length measurement</li>
                        <li><kbd>F</kbd> - Fit to window</li>
                        <li><kbd>I</kbd> - Invert colors</li>
                        <li><kbd>Ctrl+R</kbd> - Reset view</li>
                        <li><kbd>←</kbd> - Previous frame</li>
                        <li><kbd>→</kbd> - Next frame</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="viewer.js"></script>
</body>
</html>