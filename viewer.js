/**
 * Modern DICOM Viewer using Cornerstone.js
 * Professional DICOM viewer with comprehensive tools and modern UI
 */

// Global variables
let element;
let currentImageId = null;
let currentImageIndex = 0;
let loadedFiles = [];
let currentTool = 'Wwwc';

// Check if all required dependencies are loaded
if (typeof cornerstone === 'undefined' ||
    typeof cornerstoneWADOImageLoader === 'undefined' ||
    typeof cornerstoneTools === 'undefined' ||
    typeof dicomParser === 'undefined' ||
    typeof cornerstoneMath === 'undefined') {
    console.error('Required Cornerstone dependencies are not loaded');
    document.addEventListener('DOMContentLoaded', function() {
        showError('Failed to load Cornerstone dependencies. Please refresh the page.');
    });
} else {
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeViewer);
}

/**
 * Initialize the DICOM viewer
 */
function initializeViewer() {
    try {
        // Initialize Cornerstone
        initializeCornerstone();

        // Setup UI event listeners
        setupEventListeners();

        // Initialize theme
        initializeTheme();

        console.log('DICOM Viewer initialized successfully');
    } catch (error) {
        console.error('Failed to initialize DICOM viewer:', error);
        showError('Failed to initialize DICOM viewer. Please refresh the page.');
    }
}

/**
 * Initialize Cornerstone and its dependencies
 */
function initializeCornerstone() {
    // Configure DICOM Image Loader
    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;

    // Configure Cornerstone Tools
    cornerstoneTools.external.cornerstone = cornerstone;
    cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
    cornerstoneTools.external.Hammer = Hammer;

    // Initialize Cornerstone Tools
    cornerstoneTools.init();

    // Initialize web workers for DICOM parsing
    const config = {
        maxWebWorkers: navigator.hardwareConcurrency || 1,
        startWebWorkersOnDemand: true,
        taskConfiguration: {
            decodeTask: {
                initializeCodecsOnStartup: false,
                usePDFJS: false,
                strict: false,
            },
        },
    };

    cornerstoneWADOImageLoader.webWorkerManager.initialize(config);

    // Get the viewport element
    element = document.getElementById('viewport');

    // Enable the element for Cornerstone
    cornerstone.enable(element);

    // Setup tools
    setupTools();
}

/**
 * Setup Cornerstone tools
 */
function setupTools() {
    // Add tools to Cornerstone Tools
    cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
    cornerstoneTools.addTool(cornerstoneTools.PanTool);
    cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
    cornerstoneTools.addTool(cornerstoneTools.StackScrollMouseWheelTool);
    cornerstoneTools.addTool(cornerstoneTools.LengthTool);
    cornerstoneTools.addTool(cornerstoneTools.AngleTool);
    cornerstoneTools.addTool(cornerstoneTools.RectangleRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.EllipticalRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.ProbeTool);
    cornerstoneTools.addTool(cornerstoneTools.RotateTool);

    // Set initial tool states
    cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
    cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 2 });
    cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 4 });
    cornerstoneTools.setToolActive('StackScrollMouseWheel', {});

    // Set other tools to enabled but not active
    cornerstoneTools.setToolEnabled('Length');
    cornerstoneTools.setToolEnabled('Angle');
    cornerstoneTools.setToolEnabled('RectangleRoi');
    cornerstoneTools.setToolEnabled('EllipticalRoi');
    cornerstoneTools.setToolEnabled('Probe');
    cornerstoneTools.setToolEnabled('Rotate');
}

/**
 * Setup UI event listeners
 */
function setupEventListeners() {
    // File input
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadArea = document.getElementById('uploadArea');

    // File upload events
    uploadBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // Toolbar events
    document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
        btn.addEventListener('click', () => setActiveTool(btn.dataset.tool));
    });

    document.querySelectorAll('.tool-btn[data-action]').forEach(btn => {
        btn.addEventListener('click', () => executeAction(btn.dataset.action));
    });

    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', toggleTheme);

    // Info button
    document.getElementById('infoBtn').addEventListener('click', () => showModal('aboutModal'));

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Viewport events
    const viewportElement = document.getElementById('viewport');
    viewportElement.addEventListener('cornerstoneimagerendered', updateViewportInfo);
    viewportElement.addEventListener('cornerstonenewimage', updateViewportInfo);
}

/**
 * Handle file selection
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    loadDicomFiles(files);
}

/**
 * Handle drag over event
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * Handle drag leave event
 */
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    const dicomFiles = files.filter(file =>
        file.name.toLowerCase().endsWith('.dcm') ||
        file.name.toLowerCase().endsWith('.dicom') ||
        file.type === 'application/dicom'
    );

    if (dicomFiles.length === 0) {
        showError('Please drop valid DICOM files (.dcm or .dicom)');
        return;
    }

    loadDicomFiles(dicomFiles);
}

/**
 * Load DICOM files
 */
async function loadDicomFiles(files) {
    if (!files || files.length === 0) return;

    console.log('Loading DICOM files:', files.length);
    showLoading(true);
    hideWelcomeScreen();

    try {
        // Clear previous files
        loadedFiles = [];
        clearFileList();

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            console.log('Processing file:', file.name, 'Size:', file.size);

            // Create image ID for the file
            const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
            console.log('Created imageId:', imageId);

            // Create file info without pre-loading (simpler approach)
            const fileInfo = {
                file: file,
                imageId: imageId,
                image: null, // Will be loaded when displayed
                name: file.name,
                size: formatFileSize(file.size),
                index: i,
                numberOfFrames: 1 // Will be detected when image is loaded
            };

            loadedFiles.push(fileInfo);
            addFileToList(fileInfo);
        }

        // Load first image
        if (loadedFiles.length > 0) {
            console.log('Loading first image...');
            await loadImage(loadedFiles[0]);
        }

    } catch (error) {
        console.error('Error loading DICOM files:', error);
        showError('Error loading DICOM files. Please ensure they are valid DICOM files.');
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Load and display a DICOM image
 */
async function loadImage(fileInfo) {
    try {
        console.log('Loading image:', fileInfo.name, 'ImageId:', fileInfo.imageId);
        showLoading(true);

        // Set current image
        currentImageId = fileInfo.imageId;
        currentImageIndex = fileInfo.index;

        // Load and display the image
        console.log('Calling cornerstone.loadImage...');
        const image = await cornerstone.loadImage(fileInfo.imageId);
        console.log('Image loaded, displaying...');

        // Load and display the image
        console.log('Loading image:', fileInfo.imageId);
        const image = await cornerstone.loadImage(fileInfo.imageId);
        cornerstone.displayImage(element, image);

        // Store the loaded image
        fileInfo.image = image;

        // Detect multi-frame DICOM
        let numberOfFrames = 1;

        console.log('Checking for multi-frame DICOM...');
        console.log('Image data available:', !!image.data);

        if (image.data) {
            try {
                // Method 1: Check NumberOfFrames tag (0028,0008)
                if (image.data.string) {
                    const framesTag = image.data.string('x00280008');
                    console.log('NumberOfFrames tag value:', framesTag);
                    if (framesTag) {
                        numberOfFrames = parseInt(framesTag) || 1;
                        console.log('Parsed frames from tag:', numberOfFrames);
                    }
                }

                // Method 2: Check elements directly
                if (numberOfFrames === 1 && image.data.elements) {
                    console.log('Checking elements for frame count...');
                    const numberOfFramesElement = image.data.elements.x00280008;
                    if (numberOfFramesElement) {
                        console.log('NumberOfFrames element:', numberOfFramesElement);
                        if (numberOfFramesElement.value) {
                            numberOfFrames = parseInt(numberOfFramesElement.value) || 1;
                            console.log('Parsed frames from element:', numberOfFrames);
                        }
                    }
                }

                // Method 3: Check via cornerstone metadata
                if (numberOfFrames === 1) {
                    const metadata = cornerstone.metaData.get('multiFrame', fileInfo.imageId);
                    if (metadata && metadata.numberOfFrames) {
                        numberOfFrames = metadata.numberOfFrames;
                        console.log('Multi-frame DICOM detected via metadata:', numberOfFrames, 'frames');
                    }
                }

            } catch (error) {
                console.log('Error checking for multi-frame:', error);
            }
        }

        console.log('Final frame count:', numberOfFrames);
        fileInfo.numberOfFrames = numberOfFrames;

        if (numberOfFrames > 1) {
            console.log('Setting up multi-frame navigation for', numberOfFrames, 'frames');

            // Create image IDs for each frame
            const frameImageIds = [];
            for (let i = 0; i < numberOfFrames; i++) {
                frameImageIds.push(fileInfo.imageId + '?frame=' + i);
            }

            // Create a stack for multi-frame navigation
            const stack = {
                imageIds: frameImageIds,
                currentImageIdIndex: 0
            };

            // Store stack information
            fileInfo.stack = stack;

            // Enable stack scrolling for multi-frame
            try {
                cornerstoneTools.addStackStateManager(element, ['stack']);
                cornerstoneTools.addToolState(element, 'stack', stack);
                console.log('Stack state added successfully');
            } catch (error) {
                console.log('Error adding stack state:', error);
            }

            // Show frame navigation controls
            showFrameNavigation(numberOfFrames);

        } else {
            // Single frame image
            hideFrameNavigation();
        }

        console.log('Image displayed successfully');

        // Update UI
        updateImageInfo(fileInfo);
        updateFileListSelection(fileInfo.index);
        updateViewportInfo();

        console.log('Image loaded successfully:', fileInfo.name);

    } catch (error) {
        console.error('Error loading image:', error);
        showError('Error loading image: ' + error.message);
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Set active tool
 */
function setActiveTool(toolName) {
    if (!element) return;

    // Deactivate all tools first
    cornerstoneTools.setToolPassive('Wwwc');
    cornerstoneTools.setToolPassive('Pan');
    cornerstoneTools.setToolPassive('Zoom');
    cornerstoneTools.setToolPassive('Length');
    cornerstoneTools.setToolPassive('Angle');
    cornerstoneTools.setToolPassive('RectangleRoi');
    cornerstoneTools.setToolPassive('EllipticalRoi');
    cornerstoneTools.setToolPassive('Probe');

    // Activate new tool
    currentTool = toolName;

    try {
        switch (toolName) {
            case 'WindowLevel':
                cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
                break;
            case 'Pan':
                cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 1 });
                break;
            case 'Zoom':
                cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
                break;
            case 'Length':
                cornerstoneTools.setToolActive('Length', { mouseButtonMask: 1 });
                break;
            case 'Angle':
                cornerstoneTools.setToolActive('Angle', { mouseButtonMask: 1 });
                break;
            case 'Rectangle':
                cornerstoneTools.setToolActive('RectangleRoi', { mouseButtonMask: 1 });
                break;
            case 'Ellipse':
                cornerstoneTools.setToolActive('EllipticalRoi', { mouseButtonMask: 1 });
                break;
            case 'Probe':
                cornerstoneTools.setToolActive('Probe', { mouseButtonMask: 1 });
                break;
        }
    } catch (error) {
        console.error('Error setting tool:', error);
    }

    // Update UI
    updateToolbarSelection(toolName);
}

/**
 * Execute viewport actions
 */
function executeAction(action) {
    if (!element || !currentImageId) return;

    try {
        switch (action) {
            case 'rotate-left':
                rotateViewport(-90);
                break;
            case 'rotate-right':
                rotateViewport(90);
                break;
            case 'flip-horizontal':
                flipViewport(true, false);
                break;
            case 'flip-vertical':
                flipViewport(false, true);
                break;
            case 'reset':
                resetViewport();
                break;
            case 'fit':
                fitToWindow();
                break;
            case 'invert':
                invertColors();
                break;
            case 'clear-annotations':
                clearAnnotations();
                break;
        }
    } catch (error) {
        console.error('Error executing action:', error);
    }
}
/**
 * Viewport manipulation functions
 */
function rotateViewport(degrees) {
    const viewport = cornerstone.getViewport(element);
    viewport.rotation = (viewport.rotation || 0) + degrees;
    cornerstone.setViewport(element, viewport);
}

function flipViewport(flipHorizontal, flipVertical) {
    const viewport = cornerstone.getViewport(element);

    if (flipHorizontal) {
        viewport.hflip = !viewport.hflip;
    }
    if (flipVertical) {
        viewport.vflip = !viewport.vflip;
    }

    cornerstone.setViewport(element, viewport);
}

function resetViewport() {
    cornerstone.reset(element);
    cornerstone.fitToWindow(element);
}

function fitToWindow() {
    cornerstone.fitToWindow(element);
}

function invertColors() {
    const viewport = cornerstone.getViewport(element);
    viewport.invert = !viewport.invert;
    cornerstone.setViewport(element, viewport);
}

function clearAnnotations() {
    const toolStateManager = cornerstoneTools.globalImageIdSpecificToolStateManager;
    toolStateManager.clear(element);
    cornerstone.updateImage(element);
}

/**
 * UI Update functions
 */
function updateToolbarSelection(toolName) {
    // Remove active class from all tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to selected tool
    const activeBtn = document.querySelector(`[data-tool="${toolName}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

function updateImageInfo(fileInfo) {
    const imageInfoElement = document.getElementById('imageInfo');
    const image = fileInfo.image;

    if (!image) {
        imageInfoElement.innerHTML = '<div class="no-image"><i class="fas fa-image"></i><p>No image data</p></div>';
        return;
    }

    // Get DICOM metadata for patient/study information
    let metadata = {};
    let patientModule = {};
    let studyModule = {};
    let seriesModule = {};

    if (currentImageId) {
        try {
            metadata = cornerstone.metaData.get('generalImageModule', currentImageId) || {};
            patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
            studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};
            seriesModule = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        } catch (error) {
            console.log('Some metadata not available:', error);
        }
    }

    // Only show essential patient/study information
    const info = [
        { label: 'Patient Name', value: patientModule.patientName || 'N/A' },
        { label: 'Patient ID', value: patientModule.patientId || 'N/A' },
        { label: 'Study Date', value: studyModule.studyDate || 'N/A' },
        { label: 'Study Description', value: studyModule.studyDescription || 'N/A' },
        { label: 'Modality', value: seriesModule.modality || 'N/A' },
        { label: 'Series Description', value: seriesModule.seriesDescription || 'N/A' },
        { label: 'Institution', value: metadata.institutionName || 'N/A' },
    ];

    // Add a button to view full metadata
    const metadataButton = `
        <div class="info-item metadata-button-container">
            <button class="metadata-btn" onclick="showModal('metadataModal')" title="View complete DICOM metadata">
                <i class="fas fa-tags"></i>
                View Full Metadata
            </button>
        </div>
    `;

    imageInfoElement.innerHTML = info.map(item =>
        `<div class="info-item">
            <span class="info-label">${item.label}:</span>
            <span class="info-value">${item.value}</span>
        </div>`
    ).join('') + metadataButton;
}

function updateViewportInfo() {
    if (!element || !currentImageId) return;

    try {
        const viewport = cornerstone.getViewport(element);

        // Update patient info (if available)
        updatePatientInfo();

        // Update image info overlay
        const imageInfoOverlay = document.getElementById('imageInfoOverlay');
        if (imageInfoOverlay) {
            let imageInfo = `<div>Image: ${currentImageIndex + 1}/${loadedFiles.length}</div>`;

            // Add frame info for multi-frame DICOM
            const stackData = cornerstoneTools.getToolState(element, 'stack');
            if (stackData && stackData.data && stackData.data.length > 0) {
                const stack = stackData.data[0];
                if (stack.imageIds.length > 1) {
                    imageInfo += `<div>Frame: ${stack.currentImageIdIndex + 1}/${stack.imageIds.length}</div>`;
                }
            }

            imageInfoOverlay.innerHTML = imageInfo;
        }

        // Update window/level info
        const windowLevelInfo = document.getElementById('windowLevelInfo');
        if (windowLevelInfo && viewport) {
            windowLevelInfo.innerHTML = `
                <div>W: ${Math.round(viewport.voi.windowWidth || 0)}</div>
                <div>L: ${Math.round(viewport.voi.windowCenter || 0)}</div>
            `;
        }

        // Update zoom info
        const zoomInfo = document.getElementById('zoomInfo');
        if (zoomInfo && viewport) {
            const zoom = (viewport.scale * 100).toFixed(0);
            zoomInfo.innerHTML = `<div>Zoom: ${zoom}%</div>`;
        }

    } catch (error) {
        console.error('Error updating viewport info:', error);
    }
}

function updatePatientInfo() {
    const patientInfoElement = document.getElementById('patientInfo');
    if (!patientInfoElement || !currentImageId) return;

    try {
        // Get DICOM metadata
        const metadata = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        const patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
        const studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};

        const patientName = patientModule.patientName || 'Unknown';
        const patientId = patientModule.patientId || 'Unknown';
        const studyDate = studyModule.studyDate || 'Unknown';
        const modality = metadata.modality || 'Unknown';

        patientInfoElement.innerHTML = `
            <div>${patientName}</div>
            <div>ID: ${patientId}</div>
            <div>${modality} - ${studyDate}</div>
        `;
    } catch (error) {
        console.error('Error updating patient info:', error);
        patientInfoElement.innerHTML = '<div>Patient Info Unavailable</div>';
    }
}

function addFileToList(fileInfo) {
    const fileList = document.getElementById('fileList');

    // Remove "no files" message if it exists
    const noFiles = fileList.querySelector('.no-files');
    if (noFiles) {
        noFiles.remove();
    }

    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.dataset.index = fileInfo.index;

    fileItem.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file-medical"></i>
        </div>
        <div class="file-info">
            <div class="file-name" title="${fileInfo.name}">${fileInfo.name}</div>
            <div class="file-size">${fileInfo.size}</div>
        </div>
    `;

    fileItem.addEventListener('click', () => {
        loadImage(fileInfo);
    });

    fileList.appendChild(fileItem);
}

function updateFileListSelection(index) {
    document.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[data-index="${index}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

function clearFileList() {
    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '<div class="no-files"><i class="fas fa-folder-open"></i><p>No files loaded</p></div>';
}
/**
 * Utility functions
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
}

function hideWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'none';
    }
}

function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'flex';
    }
}

/**
 * Frame navigation functions for multi-frame DICOM
 */
function showFrameNavigation(numberOfFrames) {
    const frameNav = document.getElementById('frameNavigation');
    if (frameNav) {
        frameNav.style.display = 'flex';
        updateFrameInfo(1, numberOfFrames);
    }
}

function hideFrameNavigation() {
    const frameNav = document.getElementById('frameNavigation');
    if (frameNav) {
        frameNav.style.display = 'none';
    }
}

function updateFrameInfo(currentFrame, totalFrames) {
    const frameInfo = document.getElementById('frameInfo');
    if (frameInfo) {
        frameInfo.textContent = `Frame ${currentFrame} of ${totalFrames}`;
    }
}

function previousFrame() {
    if (!element || !currentImageId) return;

    const stackData = cornerstoneTools.getToolState(element, 'stack');
    if (stackData && stackData.data && stackData.data.length > 0) {
        const stack = stackData.data[0];
        if (stack.currentImageIdIndex > 0) {
            stack.currentImageIdIndex--;
            cornerstone.loadImage(stack.imageIds[stack.currentImageIdIndex]).then(image => {
                cornerstone.displayImage(element, image);
                updateFrameInfo(stack.currentImageIdIndex + 1, stack.imageIds.length);
                updateViewportInfo();
            });
        }
    }
}

function nextFrame() {
    if (!element || !currentImageId) return;

    const stackData = cornerstoneTools.getToolState(element, 'stack');
    if (stackData && stackData.data && stackData.data.length > 0) {
        const stack = stackData.data[0];
        if (stack.currentImageIdIndex < stack.imageIds.length - 1) {
            stack.currentImageIdIndex++;
            cornerstone.loadImage(stack.imageIds[stack.currentImageIdIndex]).then(image => {
                cornerstone.displayImage(element, image);
                updateFrameInfo(stack.currentImageIdIndex + 1, stack.imageIds.length);
                updateViewportInfo();
            });
        }
    }
}

function goToFrame(frameNumber) {
    if (!element || !currentImageId) return;

    const stackData = cornerstoneTools.getToolState(element, 'stack');
    if (stackData && stackData.data && stackData.data.length > 0) {
        const stack = stackData.data[0];
        const frameIndex = frameNumber - 1; // Convert to 0-based index
        if (frameIndex >= 0 && frameIndex < stack.imageIds.length) {
            stack.currentImageIdIndex = frameIndex;
            cornerstone.loadImage(stack.imageIds[stack.currentImageIdIndex]).then(image => {
                cornerstone.displayImage(element, image);
                updateFrameInfo(stack.currentImageIdIndex + 1, stack.imageIds.length);
                updateViewportInfo();
            });
        }
    }
}

/**
 * Test multi-frame functionality
 */
function testMultiFrame() {
    if (!element || !currentImageId) {
        alert('Please load a DICOM file first');
        return;
    }

    console.log('Testing multi-frame functionality...');

    // Force create a multi-frame stack for testing
    const testFrameIds = [];
    for (let i = 0; i < 17; i++) {
        testFrameIds.push(currentImageId + '?frame=' + i);
    }

    const testStack = {
        imageIds: testFrameIds,
        currentImageIdIndex: 0
    };

    try {
        // Clear existing stack state
        cornerstoneTools.clearToolState(element, 'stack');

        // Add new stack state
        cornerstoneTools.addStackStateManager(element, ['stack']);
        cornerstoneTools.addToolState(element, 'stack', testStack);

        // Show frame navigation
        showFrameNavigation(17);

        console.log('Test multi-frame stack created with 17 frames');
        alert('Test multi-frame created! Try using the frame navigation buttons or arrow keys.');

    } catch (error) {
        console.error('Error creating test multi-frame:', error);
        alert('Error creating test multi-frame: ' + error.message);
    }
}

function showError(message) {
    // Simple error display - could be enhanced with a proper modal
    alert('Error: ' + message);
    console.error('DICOM Viewer Error:', message);
}

/**
 * Theme management
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('dicom-viewer-theme') || 'light';
    setTheme(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('dicom-viewer-theme', theme);

    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

/**
 * Modal management
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');

        // Load content for specific modals
        if (modalId === 'metadataModal') {
            loadMetadata();
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

function loadMetadata() {
    const metadataContent = document.getElementById('metadataContent');
    if (!metadataContent || !currentImageId) return;

    try {
        // Get current file info
        const currentFile = loadedFiles[currentImageIndex];
        const image = currentFile ? currentFile.image : null;

        // Get DICOM metadata
        let metadata = {};
        let patientModule = {};
        let studyModule = {};
        let seriesModule = {};

        try {
            metadata = cornerstone.metaData.get('generalImageModule', currentImageId) || {};
            patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
            studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};
            seriesModule = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        } catch (error) {
            console.log('Some metadata not available:', error);
        }

        // Organize metadata into logical sections
        const sections = [
            {
                title: 'File Information',
                items: [
                    { label: 'File Name', value: currentFile ? currentFile.name : 'N/A' },
                    { label: 'File Size', value: currentFile ? currentFile.size : 'N/A' },
                ]
            },
            {
                title: 'Patient Information',
                items: [
                    { label: 'Patient Name', value: patientModule.patientName || 'N/A' },
                    { label: 'Patient ID', value: patientModule.patientId || 'N/A' },
                    { label: 'Patient Birth Date', value: patientModule.patientBirthDate || 'N/A' },
                    { label: 'Patient Sex', value: patientModule.patientSex || 'N/A' },
                    { label: 'Patient Age', value: patientModule.patientAge || 'N/A' },
                ]
            },
            {
                title: 'Study Information',
                items: [
                    { label: 'Study Date', value: studyModule.studyDate || 'N/A' },
                    { label: 'Study Time', value: studyModule.studyTime || 'N/A' },
                    { label: 'Study Description', value: studyModule.studyDescription || 'N/A' },
                    { label: 'Study Instance UID', value: studyModule.studyInstanceUID || 'N/A' },
                    { label: 'Accession Number', value: studyModule.accessionNumber || 'N/A' },
                    { label: 'Referring Physician', value: studyModule.referringPhysicianName || 'N/A' },
                ]
            },
            {
                title: 'Series Information',
                items: [
                    { label: 'Modality', value: seriesModule.modality || 'N/A' },
                    { label: 'Series Description', value: seriesModule.seriesDescription || 'N/A' },
                    { label: 'Series Number', value: seriesModule.seriesNumber || 'N/A' },
                    { label: 'Series Date', value: seriesModule.seriesDate || 'N/A' },
                    { label: 'Series Time', value: seriesModule.seriesTime || 'N/A' },
                    { label: 'Body Part Examined', value: seriesModule.bodyPartExamined || 'N/A' },
                ]
            },
            {
                title: 'Image Properties',
                items: [
                    { label: 'Dimensions', value: image ? `${image.width} × ${image.height}` : 'N/A' },
                    { label: 'Pixel Spacing', value: image && image.rowPixelSpacing ? `${image.rowPixelSpacing.toFixed(2)} × ${image.columnPixelSpacing.toFixed(2)} mm` : 'N/A' },
                    { label: 'Slice Thickness', value: image && image.sliceThickness ? `${image.sliceThickness} mm` : 'N/A' },
                    { label: 'Window Center', value: image && image.windowCenter ? image.windowCenter : 'N/A' },
                    { label: 'Window Width', value: image && image.windowWidth ? image.windowWidth : 'N/A' },
                    { label: 'Instance Number', value: metadata.instanceNumber || 'N/A' },
                ]
            },
            {
                title: 'Equipment Information',
                items: [
                    { label: 'Institution', value: metadata.institutionName || 'N/A' },
                    { label: 'Manufacturer', value: metadata.manufacturer || 'N/A' },
                    { label: 'Model', value: metadata.manufacturerModelName || 'N/A' },
                    { label: 'Software Version', value: metadata.softwareVersions || 'N/A' },
                    { label: 'Station Name', value: metadata.stationName || 'N/A' },
                ]
            }
        ];

        // Format metadata for display
        let html = '';
        sections.forEach(section => {
            html += `<h4>${section.title}</h4><div class="metadata-module">`;
            section.items.forEach(item => {
                html += `<div class="metadata-item">
                    <span class="metadata-key">${item.label}:</span>
                    <span class="metadata-value">${item.value}</span>
                </div>`;
            });
            html += '</div>';
        });

        if (html === '') {
            html = '<div class="no-metadata">No metadata available for this image.</div>';
        }

        metadataContent.innerHTML = html;

    } catch (error) {
        console.error('Error loading metadata:', error);
        metadataContent.innerHTML = '<div class="error">Error loading metadata.</div>';
    }
}

/**
 * Keyboard shortcuts
 */
function handleKeyboardShortcuts(event) {
    // Don't handle shortcuts if user is typing in an input
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    const key = event.key.toLowerCase();
    const ctrlKey = event.ctrlKey || event.metaKey;

    switch (key) {
        case 'w':
            event.preventDefault();
            setActiveTool('WindowLevel');
            break;
        case 'p':
            if (!ctrlKey) {
                event.preventDefault();
                setActiveTool('Pan');
            } else {
                event.preventDefault();
                setActiveTool('Probe');
            }
            break;
        case 'z':
            event.preventDefault();
            setActiveTool('Zoom');
            break;
        case 's':
            event.preventDefault();
            setActiveTool('StackScroll');
            break;
        case 'l':
            event.preventDefault();
            setActiveTool('Length');
            break;
        case 'a':
            event.preventDefault();
            setActiveTool('Angle');
            break;
        case 'r':
            if (ctrlKey) {
                event.preventDefault();
                executeAction('reset');
            } else {
                event.preventDefault();
                setActiveTool('Rectangle');
            }
            break;
        case 'e':
            event.preventDefault();
            setActiveTool('Ellipse');
            break;
        case 'f':
            event.preventDefault();
            executeAction('fit');
            break;
        case 'i':
            event.preventDefault();
            executeAction('invert');
            break;
        case 'escape':
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show');
            });
            break;
        case 'arrowleft':
            event.preventDefault();
            previousFrame();
            break;
        case 'arrowright':
            event.preventDefault();
            nextFrame();
            break;
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});

// Prevent context menu on viewport
document.addEventListener('DOMContentLoaded', function() {
    const viewport = document.getElementById('viewport');
    if (viewport) {
        viewport.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
    }
});

console.log('Modern DICOM Viewer script loaded successfully');