// Check if all required dependencies are loaded
if (typeof cornerstone === 'undefined' || 
    typeof cornerstoneWADOImageLoader === 'undefined' || 
    typeof cornerstoneTools === 'undefined' || 
    typeof dicomParser === 'undefined' || 
    typeof cornerstoneMath === 'undefined') {
    console.error('Required Cornerstone dependencies are not loaded');
    return;
}

// Initialize Cornerstone
cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
cornerstoneTools.external.cornerstone = cornerstone;
cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
cornerstoneTools.init();

// Enable the WADO Image Loader
cornerstoneWADOImageLoader.webWorkerManager.initialize({
    maxWebWorkers: navigator.hardwareConcurrency || 1,
    startWebWorkersOnDemand: true,
});

let element;

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    // Get the display element
    element = document.getElementById('dicomImage');
    
    // Enable the element for Cornerstone
    cornerstone.enable(element);

    // Add file input listener
    document.getElementById('fileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Create a URL for the file
        const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);

        // Load and display the image
        loadAndViewImage(imageId);
    });
});

function loadAndViewImage(imageId) {
    cornerstone.loadImage(imageId).then(function(image) {
        cornerstone.displayImage(element, image);
        
        // Enable tools
        cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
        cornerstoneTools.addTool(cornerstoneTools.PanTool);
        cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
        cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
        cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 2 });
        cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 4 });
    }).catch(function(error) {
        console.error(error);
        alert('Error loading the DICOM file');
    });
}