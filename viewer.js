/**
 * Modern DICOM Viewer using Cornerstone3D
 * Professional DICOM viewer with comprehensive tools and modern UI
 */

// Global variables
let renderingEngine;
let viewport;
let currentImageId = null;
let currentImageIndex = 0;
let loadedFiles = [];
let currentTool = 'WindowLevel';

// Cornerstone3D modules
const { RenderingEngine, Enums, imageLoader, metaData, cache } = window.cornerstoneCore || {};
const { ToolGroupManager, Enums: csToolsEnums, annotation } = window.cornerstoneTools || {};
const cornerstoneDICOMImageLoader = window.cornerstoneDICOMImageLoader || {};

// Check if all required dependencies are loaded
if (!window.cornerstoneCore || !window.cornerstoneTools || !window.cornerstoneDICOMImageLoader) {
    console.error('Required Cornerstone3D dependencies are not loaded');
    document.addEventListener('DOMContentLoaded', function() {
        showError('Failed to load Cornerstone3D dependencies. Please refresh the page.');
    });
} else {
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeViewer);
}

/**
 * Initialize the DICOM viewer
 */
async function initializeViewer() {
    try {
        // Initialize Cornerstone3D
        await initializeCornerstone3D();

        // Setup UI event listeners
        setupEventListeners();

        // Initialize theme
        initializeTheme();

        console.log('DICOM Viewer initialized successfully');
    } catch (error) {
        console.error('Failed to initialize DICOM viewer:', error);
        showError('Failed to initialize DICOM viewer. Please refresh the page.');
    }
}

/**
 * Initialize Cornerstone3D and its dependencies
 */
async function initializeCornerstone3D() {
    // Initialize Cornerstone3D
    await window.cornerstoneCore.init();
    await window.cornerstoneTools.init();

    // Configure DICOM Image Loader
    cornerstoneDICOMImageLoader.external.cornerstone = window.cornerstoneCore;
    cornerstoneDICOMImageLoader.external.dicomParser = window.dicomParser;

    // Initialize web workers for DICOM parsing
    const config = {
        maxWebWorkers: navigator.hardwareConcurrency || 1,
        startWebWorkersOnDemand: true,
        taskConfiguration: {
            decodeTask: {
                initializeCodecsOnStartup: false,
                usePDFJS: false,
                strict: false,
            },
        },
    };

    cornerstoneDICOMImageLoader.webWorkerManager.initialize(config);

    // Create rendering engine
    renderingEngine = new RenderingEngine('myRenderingEngine');

    // Setup viewport
    const viewportElement = document.getElementById('viewport');
    const viewportInput = {
        viewportId: 'CT_STACK',
        type: Enums.ViewportType.STACK,
        element: viewportElement,
    };

    renderingEngine.enableElement(viewportInput);
    viewport = renderingEngine.getViewport('CT_STACK');

    // Setup tools
    await setupTools();
}

/**
 * Setup Cornerstone3D tools
 */
async function setupTools() {
    // Add tools to Cornerstone3D
    const {
        WindowLevelTool,
        PanTool,
        ZoomTool,
        StackScrollMouseWheelTool,
        LengthTool,
        AngleTool,
        RectangleROITool,
        EllipticalROITool,
        ProbeTool,
    } = window.cornerstoneTools;

    // Add tools
    window.cornerstoneTools.addTool(WindowLevelTool);
    window.cornerstoneTools.addTool(PanTool);
    window.cornerstoneTools.addTool(ZoomTool);
    window.cornerstoneTools.addTool(StackScrollMouseWheelTool);
    window.cornerstoneTools.addTool(LengthTool);
    window.cornerstoneTools.addTool(AngleTool);
    window.cornerstoneTools.addTool(RectangleROITool);
    window.cornerstoneTools.addTool(EllipticalROITool);
    window.cornerstoneTools.addTool(ProbeTool);

    // Create tool group
    const toolGroupId = 'STACK_TOOL_GROUP_ID';
    const toolGroup = ToolGroupManager.createToolGroup(toolGroupId);

    // Add tools to tool group
    toolGroup.addTool(WindowLevelTool.toolName);
    toolGroup.addTool(PanTool.toolName);
    toolGroup.addTool(ZoomTool.toolName);
    toolGroup.addTool(StackScrollMouseWheelTool.toolName);
    toolGroup.addTool(LengthTool.toolName);
    toolGroup.addTool(AngleTool.toolName);
    toolGroup.addTool(RectangleROITool.toolName);
    toolGroup.addTool(EllipticalROITool.toolName);
    toolGroup.addTool(ProbeTool.toolName);

    // Set tool modes
    toolGroup.setToolActive(WindowLevelTool.toolName, {
        bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
    });

    toolGroup.setToolActive(PanTool.toolName, {
        bindings: [{ mouseButton: csToolsEnums.MouseBindings.Auxiliary }],
    });

    toolGroup.setToolActive(ZoomTool.toolName, {
        bindings: [{ mouseButton: csToolsEnums.MouseBindings.Secondary }],
    });

    toolGroup.setToolActive(StackScrollMouseWheelTool.toolName);

    // Set other tools to passive
    toolGroup.setToolPassive(LengthTool.toolName);
    toolGroup.setToolPassive(AngleTool.toolName);
    toolGroup.setToolPassive(RectangleROITool.toolName);
    toolGroup.setToolPassive(EllipticalROITool.toolName);
    toolGroup.setToolPassive(ProbeTool.toolName);

    // Add viewport to tool group
    toolGroup.addViewport('CT_STACK', 'myRenderingEngine');
}

/**
 * Setup UI event listeners
 */
function setupEventListeners() {
    // File input
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadArea = document.getElementById('uploadArea');

    // File upload events
    uploadBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // Toolbar events
    document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
        btn.addEventListener('click', () => setActiveTool(btn.dataset.tool));
    });

    document.querySelectorAll('.tool-btn[data-action]').forEach(btn => {
        btn.addEventListener('click', () => executeAction(btn.dataset.action));
    });

    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', toggleTheme);

    // Info button
    document.getElementById('infoBtn').addEventListener('click', () => showModal('aboutModal'));

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Viewport events
    const viewportElement = document.getElementById('viewport');
    viewportElement.addEventListener('cornerstoneimagerendered', updateViewportInfo);
}

/**
 * Handle file selection
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    loadDicomFiles(files);
}

/**
 * Handle drag over event
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * Handle drag leave event
 */
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    const dicomFiles = files.filter(file =>
        file.name.toLowerCase().endsWith('.dcm') ||
        file.name.toLowerCase().endsWith('.dicom') ||
        file.type === 'application/dicom'
    );

    if (dicomFiles.length === 0) {
        showError('Please drop valid DICOM files (.dcm or .dicom)');
        return;
    }

    loadDicomFiles(dicomFiles);
}

/**
 * Load DICOM files
 */
async function loadDicomFiles(files) {
    if (!files || files.length === 0) return;

    showLoading(true);
    hideWelcomeScreen();

    try {
        // Clear previous files
        loadedFiles = [];
        clearFileList();

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const imageId = cornerstoneDICOMImageLoader.wadouri.fileManager.add(file);

            // Load image to get metadata
            const image = await imageLoader.loadImage(imageId);

            const fileInfo = {
                file: file,
                imageId: imageId,
                image: image,
                name: file.name,
                size: formatFileSize(file.size),
                index: i
            };

            loadedFiles.push(fileInfo);
            addFileToList(fileInfo);
        }

        // Load first image
        if (loadedFiles.length > 0) {
            await loadImage(loadedFiles[0]);
        }

    } catch (error) {
        console.error('Error loading DICOM files:', error);
        showError('Error loading DICOM files. Please ensure they are valid DICOM files.');
    } finally {
        showLoading(false);
    }
}

/**
 * Load and display a DICOM image
 */
async function loadImage(fileInfo) {
    try {
        showLoading(true);

        // Set current image
        currentImageId = fileInfo.imageId;
        currentImageIndex = fileInfo.index;

        // Create stack
        const stack = {
            imageIds: [fileInfo.imageId],
            currentImageIdIndex: 0,
        };

        // Set stack on viewport
        await viewport.setStack(stack);

        // Update UI
        updateImageInfo(fileInfo);
        updateFileListSelection(fileInfo.index);
        updateViewportInfo();

        console.log('Image loaded successfully:', fileInfo.name);

    } catch (error) {
        console.error('Error loading image:', error);
        showError('Error loading image: ' + error.message);
    } finally {
        showLoading(false);
    }
}

/**
 * Set active tool
 */
function setActiveTool(toolName) {
    const toolGroup = ToolGroupManager.getToolGroup('STACK_TOOL_GROUP_ID');
    if (!toolGroup) return;

    // Deactivate current tool
    if (currentTool) {
        try {
            toolGroup.setToolPassive(currentTool + 'Tool');
        } catch (e) {
            // Tool might not exist, ignore
        }
    }

    // Activate new tool
    currentTool = toolName;

    try {
        switch (toolName) {
            case 'WindowLevel':
                toolGroup.setToolActive('WindowLevelTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Pan':
                toolGroup.setToolActive('PanTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Zoom':
                toolGroup.setToolActive('ZoomTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Length':
                toolGroup.setToolActive('LengthTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Angle':
                toolGroup.setToolActive('AngleTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Rectangle':
                toolGroup.setToolActive('RectangleROITool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Ellipse':
                toolGroup.setToolActive('EllipticalROITool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
            case 'Probe':
                toolGroup.setToolActive('ProbeTool', {
                    bindings: [{ mouseButton: csToolsEnums.MouseBindings.Primary }],
                });
                break;
        }
    } catch (error) {
        console.error('Error setting tool:', error);
    }

    // Update UI
    updateToolbarSelection(toolName);
}

/**
 * Execute viewport actions
 */
function executeAction(action) {
    if (!viewport || !currentImageId) return;

    try {
        switch (action) {
            case 'rotate-left':
                rotateViewport(-90);
                break;
            case 'rotate-right':
                rotateViewport(90);
                break;
            case 'flip-horizontal':
                flipViewport(true, false);
                break;
            case 'flip-vertical':
                flipViewport(false, true);
                break;
            case 'reset':
                resetViewport();
                break;
            case 'fit':
                fitToWindow();
                break;
            case 'invert':
                invertColors();
                break;
            case 'clear-annotations':
                clearAnnotations();
                break;
        }
    } catch (error) {
        console.error('Error executing action:', error);
    }
}
/**
 * Viewport manipulation functions
 */
function rotateViewport(degrees) {
    const properties = viewport.getProperties();
    const newRotation = (properties.rotation || 0) + degrees;
    viewport.setProperties({ rotation: newRotation });
    viewport.render();
}

function flipViewport(flipHorizontal, flipVertical) {
    const properties = viewport.getProperties();
    const newProps = {};

    if (flipHorizontal) {
        newProps.flipHorizontal = !properties.flipHorizontal;
    }
    if (flipVertical) {
        newProps.flipVertical = !properties.flipVertical;
    }

    viewport.setProperties(newProps);
    viewport.render();
}

function resetViewport() {
    viewport.resetProperties();
    viewport.render();
}

function fitToWindow() {
    viewport.resetProperties();
    viewport.render();
}

function invertColors() {
    const properties = viewport.getProperties();
    viewport.setProperties({ invert: !properties.invert });
    viewport.render();
}

function clearAnnotations() {
    const toolGroup = ToolGroupManager.getToolGroup('STACK_TOOL_GROUP_ID');
    if (toolGroup) {
        annotation.state.removeAllAnnotations();
        viewport.render();
    }
}

/**
 * UI Update functions
 */
function updateToolbarSelection(toolName) {
    // Remove active class from all tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to selected tool
    const activeBtn = document.querySelector(`[data-tool="${toolName}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

function updateImageInfo(fileInfo) {
    const imageInfoElement = document.getElementById('imageInfo');
    const image = fileInfo.image;

    if (!image) {
        imageInfoElement.innerHTML = '<div class="no-image"><i class="fas fa-image"></i><p>No image data</p></div>';
        return;
    }

    const info = [
        { label: 'File Name', value: fileInfo.name },
        { label: 'File Size', value: fileInfo.size },
        { label: 'Dimensions', value: `${image.width} × ${image.height}` },
        { label: 'Pixel Spacing', value: image.rowPixelSpacing ? `${image.rowPixelSpacing.toFixed(2)} × ${image.columnPixelSpacing.toFixed(2)} mm` : 'N/A' },
        { label: 'Slice Thickness', value: image.sliceThickness ? `${image.sliceThickness} mm` : 'N/A' },
        { label: 'Window Center', value: image.windowCenter || 'N/A' },
        { label: 'Window Width', value: image.windowWidth || 'N/A' },
    ];

    imageInfoElement.innerHTML = info.map(item =>
        `<div class="info-item">
            <span class="info-label">${item.label}:</span>
            <span class="info-value">${item.value}</span>
        </div>`
    ).join('');
}

function updateViewportInfo() {
    if (!viewport || !currentImageId) return;

    try {
        const properties = viewport.getProperties();
        const camera = viewport.getCamera();

        // Update patient info (if available)
        updatePatientInfo();

        // Update image info overlay
        const imageInfoOverlay = document.getElementById('imageInfoOverlay');
        if (imageInfoOverlay) {
            imageInfoOverlay.innerHTML = `
                <div>Image: ${currentImageIndex + 1}/${loadedFiles.length}</div>
            `;
        }

        // Update window/level info
        const windowLevelInfo = document.getElementById('windowLevelInfo');
        if (windowLevelInfo && properties.voiRange) {
            const windowWidth = properties.voiRange.upper - properties.voiRange.lower;
            const windowCenter = (properties.voiRange.upper + properties.voiRange.lower) / 2;
            windowLevelInfo.innerHTML = `
                <div>W: ${Math.round(windowWidth)}</div>
                <div>L: ${Math.round(windowCenter)}</div>
            `;
        }

        // Update zoom info
        const zoomInfo = document.getElementById('zoomInfo');
        if (zoomInfo && camera) {
            const zoom = camera.parallelScale ? (1 / camera.parallelScale * 100).toFixed(0) : 100;
            zoomInfo.innerHTML = `<div>Zoom: ${zoom}%</div>`;
        }

    } catch (error) {
        console.error('Error updating viewport info:', error);
    }
}

function updatePatientInfo() {
    const patientInfoElement = document.getElementById('patientInfo');
    if (!patientInfoElement || !currentImageId) return;

    try {
        // Get DICOM metadata
        const metadata = metaData.get('generalSeriesModule', currentImageId) || {};
        const patientModule = metaData.get('patientModule', currentImageId) || {};
        const studyModule = metaData.get('generalStudyModule', currentImageId) || {};

        const patientName = patientModule.patientName || 'Unknown';
        const patientId = patientModule.patientId || 'Unknown';
        const studyDate = studyModule.studyDate || 'Unknown';
        const modality = metadata.modality || 'Unknown';

        patientInfoElement.innerHTML = `
            <div>${patientName}</div>
            <div>ID: ${patientId}</div>
            <div>${modality} - ${studyDate}</div>
        `;
    } catch (error) {
        console.error('Error updating patient info:', error);
        patientInfoElement.innerHTML = '<div>Patient Info Unavailable</div>';
    }
}

function addFileToList(fileInfo) {
    const fileList = document.getElementById('fileList');

    // Remove "no files" message if it exists
    const noFiles = fileList.querySelector('.no-files');
    if (noFiles) {
        noFiles.remove();
    }

    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.dataset.index = fileInfo.index;

    fileItem.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file-medical"></i>
        </div>
        <div class="file-info">
            <div class="file-name" title="${fileInfo.name}">${fileInfo.name}</div>
            <div class="file-size">${fileInfo.size}</div>
        </div>
    `;

    fileItem.addEventListener('click', () => {
        loadImage(fileInfo);
    });

    fileList.appendChild(fileItem);
}

function updateFileListSelection(index) {
    document.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[data-index="${index}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

function clearFileList() {
    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '<div class="no-files"><i class="fas fa-folder-open"></i><p>No files loaded</p></div>';
}
/**
 * Utility functions
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
}

function hideWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'none';
    }
}

function showError(message) {
    // Simple error display - could be enhanced with a proper modal
    alert('Error: ' + message);
    console.error('DICOM Viewer Error:', message);
}

/**
 * Theme management
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('dicom-viewer-theme') || 'light';
    setTheme(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('dicom-viewer-theme', theme);

    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

/**
 * Modal management
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');

        // Load content for specific modals
        if (modalId === 'metadataModal') {
            loadMetadata();
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

function loadMetadata() {
    const metadataContent = document.getElementById('metadataContent');
    if (!metadataContent || !currentImageId) return;

    try {
        // Get all available metadata
        const allMetadata = {};
        const metadataTypes = [
            'generalSeriesModule',
            'patientModule',
            'generalStudyModule',
            'generalImageModule',
            'imagePixelModule',
            'modalityLutModule',
            'voiLutModule'
        ];

        metadataTypes.forEach(type => {
            const data = metaData.get(type, currentImageId);
            if (data) {
                allMetadata[type] = data;
            }
        });

        // Format metadata for display
        let html = '';
        Object.keys(allMetadata).forEach(moduleType => {
            html += `<h4>${moduleType}</h4><div class="metadata-module">`;
            const module = allMetadata[moduleType];
            Object.keys(module).forEach(key => {
                const value = module[key];
                html += `<div class="metadata-item">
                    <span class="metadata-key">${key}:</span>
                    <span class="metadata-value">${value}</span>
                </div>`;
            });
            html += '</div>';
        });

        if (html === '') {
            html = '<div class="no-metadata">No metadata available for this image.</div>';
        }

        metadataContent.innerHTML = html;

    } catch (error) {
        console.error('Error loading metadata:', error);
        metadataContent.innerHTML = '<div class="error">Error loading metadata.</div>';
    }
}

/**
 * Keyboard shortcuts
 */
function handleKeyboardShortcuts(event) {
    // Don't handle shortcuts if user is typing in an input
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    const key = event.key.toLowerCase();
    const ctrlKey = event.ctrlKey || event.metaKey;

    switch (key) {
        case 'w':
            event.preventDefault();
            setActiveTool('WindowLevel');
            break;
        case 'p':
            if (!ctrlKey) {
                event.preventDefault();
                setActiveTool('Pan');
            } else {
                event.preventDefault();
                setActiveTool('Probe');
            }
            break;
        case 'z':
            event.preventDefault();
            setActiveTool('Zoom');
            break;
        case 's':
            event.preventDefault();
            setActiveTool('StackScroll');
            break;
        case 'l':
            event.preventDefault();
            setActiveTool('Length');
            break;
        case 'a':
            event.preventDefault();
            setActiveTool('Angle');
            break;
        case 'r':
            if (ctrlKey) {
                event.preventDefault();
                executeAction('reset');
            } else {
                event.preventDefault();
                setActiveTool('Rectangle');
            }
            break;
        case 'e':
            event.preventDefault();
            setActiveTool('Ellipse');
            break;
        case 'f':
            event.preventDefault();
            executeAction('fit');
            break;
        case 'i':
            event.preventDefault();
            executeAction('invert');
            break;
        case 'escape':
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show');
            });
            break;
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});

// Prevent context menu on viewport
document.addEventListener('DOMContentLoaded', function() {
    const viewport = document.getElementById('viewport');
    if (viewport) {
        viewport.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
    }
});

console.log('Modern DICOM Viewer script loaded successfully');