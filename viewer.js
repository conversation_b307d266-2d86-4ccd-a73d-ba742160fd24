/**
 * Modern DICOM Viewer using Cornerstone.js
 * Professional DICOM viewer with comprehensive tools and modern UI
 */

// Global variables
let element;
let currentImageId = null;
let currentImageIndex = 0;
let loadedFiles = [];
let currentTool = 'Wwwc';

// Check if all required dependencies are loaded
if (typeof cornerstone === 'undefined' ||
    typeof cornerstoneWADOImageLoader === 'undefined' ||
    typeof cornerstoneTools === 'undefined' ||
    typeof dicomParser === 'undefined' ||
    typeof cornerstoneMath === 'undefined') {
    console.error('Required Cornerstone dependencies are not loaded');
    document.addEventListener('DOMContentLoaded', function() {
        showError('Failed to load Cornerstone dependencies. Please refresh the page.');
    });
} else {
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeViewer);
}

/**
 * Initialize the DICOM viewer
 */
function initializeViewer() {
    try {
        // Initialize Cornerstone
        initializeCornerstone();

        // Setup UI event listeners
        setupEventListeners();

        // Initialize theme
        initializeTheme();

        console.log('DICOM Viewer initialized successfully');
    } catch (error) {
        console.error('Failed to initialize DICOM viewer:', error);
        showError('Failed to initialize DICOM viewer. Please refresh the page.');
    }
}

/**
 * Initialize Cornerstone and its dependencies
 */
function initializeCornerstone() {
    // Configure DICOM Image Loader
    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;

    // Configure Cornerstone Tools
    cornerstoneTools.external.cornerstone = cornerstone;
    cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
    cornerstoneTools.external.Hammer = Hammer;

    // Initialize Cornerstone Tools
    cornerstoneTools.init();

    // Initialize web workers for DICOM parsing
    const config = {
        maxWebWorkers: navigator.hardwareConcurrency || 1,
        startWebWorkersOnDemand: true,
        taskConfiguration: {
            decodeTask: {
                initializeCodecsOnStartup: false,
                usePDFJS: false,
                strict: false,
            },
        },
    };

    cornerstoneWADOImageLoader.webWorkerManager.initialize(config);

    // Get the viewport element
    element = document.getElementById('viewport');

    // Enable the element for Cornerstone
    cornerstone.enable(element);

    // Setup tools
    setupTools();
}

/**
 * Setup Cornerstone tools
 */
function setupTools() {
    // Add tools to Cornerstone Tools
    cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
    cornerstoneTools.addTool(cornerstoneTools.PanTool);
    cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
    cornerstoneTools.addTool(cornerstoneTools.StackScrollMouseWheelTool);
    cornerstoneTools.addTool(cornerstoneTools.LengthTool);
    cornerstoneTools.addTool(cornerstoneTools.AngleTool);
    cornerstoneTools.addTool(cornerstoneTools.RectangleRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.EllipticalRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.ProbeTool);
    cornerstoneTools.addTool(cornerstoneTools.RotateTool);

    // Set initial tool states
    cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
    cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 2 });
    cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 4 });
    cornerstoneTools.setToolActive('StackScrollMouseWheel', {});

    // Set other tools to enabled but not active
    cornerstoneTools.setToolEnabled('Length');
    cornerstoneTools.setToolEnabled('Angle');
    cornerstoneTools.setToolEnabled('RectangleRoi');
    cornerstoneTools.setToolEnabled('EllipticalRoi');
    cornerstoneTools.setToolEnabled('Probe');
    cornerstoneTools.setToolEnabled('Rotate');
}

/**
 * Setup UI event listeners
 */
function setupEventListeners() {
    // File input
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadArea = document.getElementById('uploadArea');

    // File upload events
    uploadBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // Toolbar events
    document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
        btn.addEventListener('click', () => setActiveTool(btn.dataset.tool));
    });

    document.querySelectorAll('.tool-btn[data-action]').forEach(btn => {
        btn.addEventListener('click', () => executeAction(btn.dataset.action));
    });

    // Theme toggle
    document.getElementById('themeToggle').addEventListener('click', toggleTheme);

    // Info button
    document.getElementById('infoBtn').addEventListener('click', () => showModal('aboutModal'));

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Viewport events
    const viewportElement = document.getElementById('viewport');
    viewportElement.addEventListener('cornerstoneimagerendered', updateViewportInfo);
    viewportElement.addEventListener('cornerstonenewimage', updateViewportInfo);
}

/**
 * Handle file selection
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    loadDicomFiles(files);
}

/**
 * Handle drag over event
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * Handle drag leave event
 */
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    const dicomFiles = files.filter(file =>
        file.name.toLowerCase().endsWith('.dcm') ||
        file.name.toLowerCase().endsWith('.dicom') ||
        file.type === 'application/dicom'
    );

    if (dicomFiles.length === 0) {
        showError('Please drop valid DICOM files (.dcm or .dicom)');
        return;
    }

    loadDicomFiles(dicomFiles);
}

/**
 * Load DICOM files
 */
async function loadDicomFiles(files) {
    if (!files || files.length === 0) return;

    console.log('Loading DICOM files:', files.length);
    showLoading(true);
    hideWelcomeScreen();

    try {
        // Clear previous files
        loadedFiles = [];
        clearFileList();

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            console.log('Processing file:', file.name, 'Size:', file.size);

            // Create image ID for the file
            const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
            console.log('Created imageId:', imageId);

            const fileInfo = {
                file: file,
                imageId: imageId,
                image: null, // Will be loaded when displayed
                name: file.name,
                size: formatFileSize(file.size),
                index: i
            };

            loadedFiles.push(fileInfo);
            addFileToList(fileInfo);
        }

        // Load first image
        if (loadedFiles.length > 0) {
            console.log('Loading first image...');
            await loadImage(loadedFiles[0]);
        }

    } catch (error) {
        console.error('Error loading DICOM files:', error);
        showError('Error loading DICOM files. Please ensure they are valid DICOM files.');
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Load and display a DICOM image
 */
async function loadImage(fileInfo) {
    try {
        console.log('Loading image:', fileInfo.name, 'ImageId:', fileInfo.imageId);
        showLoading(true);

        // Set current image
        currentImageId = fileInfo.imageId;
        currentImageIndex = fileInfo.index;

        // Load and display the image
        console.log('Calling cornerstone.loadImage...');
        const image = await cornerstone.loadImage(fileInfo.imageId);
        console.log('Image loaded, displaying...');
        cornerstone.displayImage(element, image);
        console.log('Image displayed successfully');

        // Store the loaded image in fileInfo
        fileInfo.image = image;

        // Update UI
        updateImageInfo(fileInfo);
        updateFileListSelection(fileInfo.index);
        updateViewportInfo();

        console.log('Image loaded successfully:', fileInfo.name);

    } catch (error) {
        console.error('Error loading image:', error);
        showError('Error loading image: ' + error.message);
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Set active tool
 */
function setActiveTool(toolName) {
    if (!element) return;

    // Deactivate all tools first
    cornerstoneTools.setToolPassive('Wwwc');
    cornerstoneTools.setToolPassive('Pan');
    cornerstoneTools.setToolPassive('Zoom');
    cornerstoneTools.setToolPassive('Length');
    cornerstoneTools.setToolPassive('Angle');
    cornerstoneTools.setToolPassive('RectangleRoi');
    cornerstoneTools.setToolPassive('EllipticalRoi');
    cornerstoneTools.setToolPassive('Probe');

    // Activate new tool
    currentTool = toolName;

    try {
        switch (toolName) {
            case 'WindowLevel':
                cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
                break;
            case 'Pan':
                cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 1 });
                break;
            case 'Zoom':
                cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
                break;
            case 'Length':
                cornerstoneTools.setToolActive('Length', { mouseButtonMask: 1 });
                break;
            case 'Angle':
                cornerstoneTools.setToolActive('Angle', { mouseButtonMask: 1 });
                break;
            case 'Rectangle':
                cornerstoneTools.setToolActive('RectangleRoi', { mouseButtonMask: 1 });
                break;
            case 'Ellipse':
                cornerstoneTools.setToolActive('EllipticalRoi', { mouseButtonMask: 1 });
                break;
            case 'Probe':
                cornerstoneTools.setToolActive('Probe', { mouseButtonMask: 1 });
                break;
        }
    } catch (error) {
        console.error('Error setting tool:', error);
    }

    // Update UI
    updateToolbarSelection(toolName);
}

/**
 * Execute viewport actions
 */
function executeAction(action) {
    if (!element || !currentImageId) return;

    try {
        switch (action) {
            case 'rotate-left':
                rotateViewport(-90);
                break;
            case 'rotate-right':
                rotateViewport(90);
                break;
            case 'flip-horizontal':
                flipViewport(true, false);
                break;
            case 'flip-vertical':
                flipViewport(false, true);
                break;
            case 'reset':
                resetViewport();
                break;
            case 'fit':
                fitToWindow();
                break;
            case 'invert':
                invertColors();
                break;
            case 'clear-annotations':
                clearAnnotations();
                break;
        }
    } catch (error) {
        console.error('Error executing action:', error);
    }
}
/**
 * Viewport manipulation functions
 */
function rotateViewport(degrees) {
    const viewport = cornerstone.getViewport(element);
    viewport.rotation = (viewport.rotation || 0) + degrees;
    cornerstone.setViewport(element, viewport);
}

function flipViewport(flipHorizontal, flipVertical) {
    const viewport = cornerstone.getViewport(element);

    if (flipHorizontal) {
        viewport.hflip = !viewport.hflip;
    }
    if (flipVertical) {
        viewport.vflip = !viewport.vflip;
    }

    cornerstone.setViewport(element, viewport);
}

function resetViewport() {
    cornerstone.reset(element);
    cornerstone.fitToWindow(element);
}

function fitToWindow() {
    cornerstone.fitToWindow(element);
}

function invertColors() {
    const viewport = cornerstone.getViewport(element);
    viewport.invert = !viewport.invert;
    cornerstone.setViewport(element, viewport);
}

function clearAnnotations() {
    const toolStateManager = cornerstoneTools.globalImageIdSpecificToolStateManager;
    toolStateManager.clear(element);
    cornerstone.updateImage(element);
}

/**
 * UI Update functions
 */
function updateToolbarSelection(toolName) {
    // Remove active class from all tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to selected tool
    const activeBtn = document.querySelector(`[data-tool="${toolName}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

function updateImageInfo(fileInfo) {
    const imageInfoElement = document.getElementById('imageInfo');
    const image = fileInfo.image;

    if (!image) {
        imageInfoElement.innerHTML = '<div class="no-image"><i class="fas fa-image"></i><p>No image data</p></div>';
        return;
    }

    // Get DICOM metadata for additional information
    let metadata = {};
    let patientModule = {};
    let studyModule = {};
    let seriesModule = {};

    if (currentImageId) {
        try {
            metadata = cornerstone.metaData.get('generalImageModule', currentImageId) || {};
            patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
            studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};
            seriesModule = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        } catch (error) {
            console.log('Some metadata not available:', error);
        }
    }

    const info = [
        // File Information
        { label: 'File Name', value: fileInfo.name },
        { label: 'File Size', value: fileInfo.size },

        // Image Properties
        { label: 'Dimensions', value: `${image.width} × ${image.height}` },
        { label: 'Pixel Spacing', value: image.rowPixelSpacing ? `${image.rowPixelSpacing.toFixed(2)} × ${image.columnPixelSpacing.toFixed(2)} mm` : 'N/A' },
        { label: 'Slice Thickness', value: image.sliceThickness ? `${image.sliceThickness} mm` : 'N/A' },
        { label: 'Window Center', value: image.windowCenter || 'N/A' },
        { label: 'Window Width', value: image.windowWidth || 'N/A' },

        // DICOM Metadata
        { label: 'Patient Name', value: patientModule.patientName || 'N/A' },
        { label: 'Patient ID', value: patientModule.patientId || 'N/A' },
        { label: 'Study Date', value: studyModule.studyDate || 'N/A' },
        { label: 'Study Description', value: studyModule.studyDescription || 'N/A' },
        { label: 'Modality', value: seriesModule.modality || 'N/A' },
        { label: 'Series Description', value: seriesModule.seriesDescription || 'N/A' },
        { label: 'Institution', value: metadata.institutionName || 'N/A' },
        { label: 'Manufacturer', value: metadata.manufacturer || 'N/A' },
        { label: 'Model', value: metadata.manufacturerModelName || 'N/A' },
    ];

    // Add a button to view full metadata
    const metadataButton = `
        <div class="info-item metadata-button-container">
            <button class="metadata-btn" onclick="showModal('metadataModal')" title="View complete DICOM metadata">
                <i class="fas fa-tags"></i>
                View Full Metadata
            </button>
        </div>
    `;

    imageInfoElement.innerHTML = info.map(item =>
        `<div class="info-item">
            <span class="info-label">${item.label}:</span>
            <span class="info-value">${item.value}</span>
        </div>`
    ).join('') + metadataButton;
}

function updateViewportInfo() {
    if (!element || !currentImageId) return;

    try {
        const viewport = cornerstone.getViewport(element);

        // Update patient info (if available)
        updatePatientInfo();

        // Update image info overlay
        const imageInfoOverlay = document.getElementById('imageInfoOverlay');
        if (imageInfoOverlay) {
            imageInfoOverlay.innerHTML = `
                <div>Image: ${currentImageIndex + 1}/${loadedFiles.length}</div>
            `;
        }

        // Update window/level info
        const windowLevelInfo = document.getElementById('windowLevelInfo');
        if (windowLevelInfo && viewport) {
            windowLevelInfo.innerHTML = `
                <div>W: ${Math.round(viewport.voi.windowWidth || 0)}</div>
                <div>L: ${Math.round(viewport.voi.windowCenter || 0)}</div>
            `;
        }

        // Update zoom info
        const zoomInfo = document.getElementById('zoomInfo');
        if (zoomInfo && viewport) {
            const zoom = (viewport.scale * 100).toFixed(0);
            zoomInfo.innerHTML = `<div>Zoom: ${zoom}%</div>`;
        }

    } catch (error) {
        console.error('Error updating viewport info:', error);
    }
}

function updatePatientInfo() {
    const patientInfoElement = document.getElementById('patientInfo');
    if (!patientInfoElement || !currentImageId) return;

    try {
        // Get DICOM metadata
        const metadata = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        const patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
        const studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};

        const patientName = patientModule.patientName || 'Unknown';
        const patientId = patientModule.patientId || 'Unknown';
        const studyDate = studyModule.studyDate || 'Unknown';
        const modality = metadata.modality || 'Unknown';

        patientInfoElement.innerHTML = `
            <div>${patientName}</div>
            <div>ID: ${patientId}</div>
            <div>${modality} - ${studyDate}</div>
        `;
    } catch (error) {
        console.error('Error updating patient info:', error);
        patientInfoElement.innerHTML = '<div>Patient Info Unavailable</div>';
    }
}

function addFileToList(fileInfo) {
    const fileList = document.getElementById('fileList');

    // Remove "no files" message if it exists
    const noFiles = fileList.querySelector('.no-files');
    if (noFiles) {
        noFiles.remove();
    }

    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.dataset.index = fileInfo.index;

    fileItem.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file-medical"></i>
        </div>
        <div class="file-info">
            <div class="file-name" title="${fileInfo.name}">${fileInfo.name}</div>
            <div class="file-size">${fileInfo.size}</div>
        </div>
    `;

    fileItem.addEventListener('click', () => {
        loadImage(fileInfo);
    });

    fileList.appendChild(fileItem);
}

function updateFileListSelection(index) {
    document.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[data-index="${index}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

function clearFileList() {
    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '<div class="no-files"><i class="fas fa-folder-open"></i><p>No files loaded</p></div>';
}
/**
 * Utility functions
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
}

function hideWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'none';
    }
}

function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'flex';
    }
}

function showError(message) {
    // Simple error display - could be enhanced with a proper modal
    alert('Error: ' + message);
    console.error('DICOM Viewer Error:', message);
}

/**
 * Theme management
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('dicom-viewer-theme') || 'light';
    setTheme(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('dicom-viewer-theme', theme);

    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

/**
 * Modal management
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');

        // Load content for specific modals
        if (modalId === 'metadataModal') {
            loadMetadata();
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

function loadMetadata() {
    const metadataContent = document.getElementById('metadataContent');
    if (!metadataContent || !currentImageId) return;

    try {
        // Get all available metadata
        const allMetadata = {};
        const metadataTypes = [
            'generalSeriesModule',
            'patientModule',
            'generalStudyModule',
            'generalImageModule',
            'imagePixelModule',
            'modalityLutModule',
            'voiLutModule'
        ];

        metadataTypes.forEach(type => {
            const data = cornerstone.metaData.get(type, currentImageId);
            if (data) {
                allMetadata[type] = data;
            }
        });

        // Format metadata for display
        let html = '';
        Object.keys(allMetadata).forEach(moduleType => {
            html += `<h4>${moduleType}</h4><div class="metadata-module">`;
            const module = allMetadata[moduleType];
            Object.keys(module).forEach(key => {
                const value = module[key];
                html += `<div class="metadata-item">
                    <span class="metadata-key">${key}:</span>
                    <span class="metadata-value">${value}</span>
                </div>`;
            });
            html += '</div>';
        });

        if (html === '') {
            html = '<div class="no-metadata">No metadata available for this image.</div>';
        }

        metadataContent.innerHTML = html;

    } catch (error) {
        console.error('Error loading metadata:', error);
        metadataContent.innerHTML = '<div class="error">Error loading metadata.</div>';
    }
}

/**
 * Keyboard shortcuts
 */
function handleKeyboardShortcuts(event) {
    // Don't handle shortcuts if user is typing in an input
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    const key = event.key.toLowerCase();
    const ctrlKey = event.ctrlKey || event.metaKey;

    switch (key) {
        case 'w':
            event.preventDefault();
            setActiveTool('WindowLevel');
            break;
        case 'p':
            if (!ctrlKey) {
                event.preventDefault();
                setActiveTool('Pan');
            } else {
                event.preventDefault();
                setActiveTool('Probe');
            }
            break;
        case 'z':
            event.preventDefault();
            setActiveTool('Zoom');
            break;
        case 's':
            event.preventDefault();
            setActiveTool('StackScroll');
            break;
        case 'l':
            event.preventDefault();
            setActiveTool('Length');
            break;
        case 'a':
            event.preventDefault();
            setActiveTool('Angle');
            break;
        case 'r':
            if (ctrlKey) {
                event.preventDefault();
                executeAction('reset');
            } else {
                event.preventDefault();
                setActiveTool('Rectangle');
            }
            break;
        case 'e':
            event.preventDefault();
            setActiveTool('Ellipse');
            break;
        case 'f':
            event.preventDefault();
            executeAction('fit');
            break;
        case 'i':
            event.preventDefault();
            executeAction('invert');
            break;
        case 'escape':
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show');
            });
            break;
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});

// Prevent context menu on viewport
document.addEventListener('DOMContentLoaded', function() {
    const viewport = document.getElementById('viewport');
    if (viewport) {
        viewport.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
    }
});

console.log('Modern DICOM Viewer script loaded successfully');